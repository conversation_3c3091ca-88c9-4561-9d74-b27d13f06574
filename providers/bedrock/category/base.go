package category

import (
	"done-hub/common/requester"
	"done-hub/providers/base"
	"done-hub/types"
	"errors"
	"net/http"
	"strings"
)

var CategoryMap = map[string]Category{}

type Category struct {
	ModelName                 string
	ChatComplete              ChatCompletionConvert
	ResponseChatComplete      ChatCompletionResponse
	ResponseChatCompleteStrem ChatCompletionStreamResponse
}

func GetCategory(modelName string) (*Category, error) {
	modelName = GetModelName(modelName)
	// 获取provider
	provider := ""

	if strings.Contains(modelName, "anthropic") {
		provider = "anthropic"
	}

	if category, exists := CategoryMap[provider]; exists {
		category.ModelName = modelName
		return &category, nil
	}

	return nil, errors.New("category_not_found")
}

func GetModelName(modelName string) string {
	bedrockMap := map[string]string{
		//cross-region model id
		"us.claude-3-sonnet-20240229":   "us.anthropic.claude-3-sonnet-20240229-v1:0",
		"us.claude-3-opus-20240229":     "us.anthropic.claude-3-opus-20240229-v1:0",
		"us.claude-3-haiku-20240307":    "us.anthropic.claude-3-haiku-20240307-v1:0",
		"us.claude-3-5-sonnet-20240620": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",
		"us.claude-3-5-sonnet-20241022": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
		"us.claude-3-5-haiku-20241022":  "us.anthropic.claude-3-5-haiku-20241022-v1:0",
		"us.claude-3-7-sonnet-20250219": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
		"us.claude-sonnet-4-20250514":   "us.anthropic.claude-sonnet-4-20250514-v1:0",
		"us.claude-opus-4-20250514":     "us.anthropic.claude-opus-4-20250514-v1:0",

		"eu.claude-3-sonnet-20240229":   "eu.anthropic.claude-3-sonnet-20240229-v1:0",
		"eu.claude-3-5-sonnet-20240620": "eu.anthropic.claude-3-5-sonnet-20240620-v1:0",
		"eu.claude-3-haiku-20240307":    "eu.anthropic.claude-3-haiku-20240307-v1:0",
		"eu.claude-3-7-sonnet-20250219": "eu.anthropic.claude-3-7-sonnet-20250219-v1:0",
		"eu.claude-sonnet-4-20250514":   "eu.anthropic.claude-sonnet-4-20250514-v1:0",
		"eu.claude-opus-4-20250514":     "eu.anthropic.claude-opus-4-20250514-v1:0",

		"apac.claude-3-sonnet-20240229":   "apac.anthropic.claude-3-sonnet-20240229-v1:0",
		"apac.claude-3-5-sonnet-20240620": "apac.anthropic.claude-3-5-sonnet-20240620-v1:0",
		"apac.claude-3-haiku-20240307":    "apac.anthropic.claude-3-haiku-20240307-v1:0",
		"apac.claude-3-7-sonnet-20250219": "apac.anthropic.claude-3-7-sonnet-20250219-v1:0",
		"apac.claude-sonnet-4-20250514":   "apac.anthropic.claude-sonnet-4-20250514-v1:0",
		"apac.claude-opus-4-20250514":     "apac.anthropic.claude-opus-4-20250514-v1:0",

		//base model id
		"claude-3-7-sonnet-20250219": "anthropic.claude-3-7-sonnet-20250219-v1:0",
		"claude-3-5-sonnet-20240620": "anthropic.claude-3-5-sonnet-20240620-v1:0",
		"claude-3-5-sonnet-20241022": "anthropic.claude-3-5-sonnet-20241022-v2:0",
		"claude-3-opus-20240229":     "anthropic.claude-3-opus-20240229-v1:0",
		"claude-3-sonnet-20240229":   "anthropic.claude-3-sonnet-20240229-v1:0",
		"claude-3-haiku-20240307":    "anthropic.claude-3-haiku-20240307-v1:0",
		"claude-3-5-haiku-20241022":  "anthropic.claude-3-5-haiku-20241022-v1:0",
		"claude-2.1":                 "anthropic.claude-v2:1",
		"claude-2.0":                 "anthropic.claude-v2",
		"claude-instant-1.2":         "anthropic.claude-instant-v1",
		"claude-sonnet-4-20250514":   "anthropic.claude-sonnet-4-20250514-v1:0",
		"claude-opus-4-20250514":     "anthropic.claude-opus-4-20250514-v1:0",
	}

	if value, exists := bedrockMap[modelName]; exists {
		modelName = value
	}

	return modelName
}

type ChatCompletionConvert func(*types.ChatCompletionRequest) (any, *types.OpenAIErrorWithStatusCode)
type ChatCompletionResponse func(base.ProviderInterface, *http.Response, *types.ChatCompletionRequest) (*types.ChatCompletionResponse, *types.OpenAIErrorWithStatusCode)

type ChatCompletionStreamResponse func(base.ProviderInterface, *types.ChatCompletionRequest) requester.HandlerPrefix[string]
