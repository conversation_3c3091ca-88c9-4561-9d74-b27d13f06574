<svg width="32" height="32" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" aria-label="AI Avatar Aura of Thought">
    <defs>
        <linearGradient id="auraGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8e83f5; stop-opacity:1" />
            <stop offset="100%" style="stop-color:#63b8f5; stop-opacity:1" />
        </linearGradient>
    </defs>

    <g transform="translate(50 50)">
        <!-- 轨道 1 -->
        <ellipse cx="0" cy="0" rx="40" ry="15" transform="rotate(20)" fill="none" stroke="url(#auraGradient)" stroke-width="1" stroke-opacity="0.6"/>

        <!-- 轨道 2 (上面有卫星) -->
        <ellipse cx="0" cy="0" rx="35" ry="28" transform="rotate(-30)" fill="none" stroke="url(#auraGradient)" stroke-width="1" stroke-opacity="0.8"/>
        <circle cx="-24.7" cy="-24.7" r="2.5" fill="url(#auraGradient)" transform="rotate(-30)"/>

        <!-- 轨道 3 -->
        <ellipse cx="0" cy="0" rx="25" ry="25" fill="none" stroke="url(#auraGradient)" stroke-width="0.75" stroke-opacity="0.4"/>

        <!-- 中心核心 -->
        <circle cx="0" cy="0" r="8" fill="url(#auraGradient)" />
    </g>
</svg>
