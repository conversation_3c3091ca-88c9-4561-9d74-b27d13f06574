// paper & background
$paper: #ffffff;

// primary
$primaryLight: #F0F7FF;
$primaryMain: #0884DD;
$primaryDark: #0062A3;
$primary200: #60B8FF;
$primary800: #004D85;
$primaryDark2: #3399E5;
// $main: #078DEE;

// secondary
$secondaryLight: #F6F0FF;
$secondaryMain: #7B61FF;
$secondaryDark: #5A3FD6;
$secondary200: #B4A1FF;
$secondary800: #4526BF;

// success Colors
$successLight: #E6F8F1;
$success200: #66D9AB;
$successMain: #22C882;
$successDark: #0AA366;

// error
$errorLight: #FFEBEE;
$errorMain: #FF4D4F;
$errorDark: #CF1322;

// orange
$orangeLight: #FFF3E0;
$orangeMain: #FF9800;
$orangeDark: #E65100;

// warning
$warningLight: #FFF8E1;
$warningMain: #FAAD14;
$warningDark: #D48806;

// grey
$grey50: #FAFAFA;
$grey100: #F5F5F5;
$grey200: #EEEEEE;
$grey300: #E0E0E0;
$grey500: #9E9E9E;
$grey600: #757575;
$grey700: #616161;
$grey900: #212121;

$tableBackground: #F8FAFC;
$tableBorderBottom: #F0F2F5;

// ==============================|| DARK THEME VARIANTS ||============================== //

// paper & background
$darkBackground: #101820; // level 3
$darkPaper: #1E2A38; // level 4
$darkDivider: rgba(240, 247, 255, 0.12);
$darkSelectedBack : rgba(0, 132, 221, 0.12);

// dark 800 & 900
$darkLevel1: #131E29; // level 1
$darkLevel2: #1A2736; // level 2

$darkTableHeader: #253545;

// primary dark
$darkPrimaryLight: #60B8FF;
$darkPrimaryMain: #0884DD;
$darkPrimaryDark: #0062A3;
$darkPrimary200: #91CAFF;
$darkPrimary800: #004D85;

// secondary dark
$darkSecondaryLight: #B4A1FF;
$darkSecondaryMain: #7B61FF;
$darkSecondaryDark: #5A3FD6;
$darkSecondary200: #D4C8FF;
$darkSecondary800: #4526BF;

// text variants
$darkTextTitle: #F5F5F7;
$darkTextPrimary: #FFFFFF;
$darkTextSecondary: #D0D5DD;
$darkTextDark: #E6E8EC;

// ==============================|| JAVASCRIPT ||============================== //

:export {
  // paper & background
  paper: $paper;

  // primary
  primaryLight: $primaryLight;
  primary200: $primary200;
  primaryMain: $primaryMain;
  primaryDark: $primaryDark;
  primary800: $primary800;

  // secondary
  secondaryLight: $secondaryLight;
  secondary200: $secondary200;
  secondaryMain: $secondaryMain;
  secondaryDark: $secondaryDark;
  secondary800: $secondary800;

  // success
  successLight: $successLight;
  success200: $success200;
  successMain: $successMain;
  successDark: $successDark;

  // error
  errorLight: $errorLight;
  errorMain: $errorMain;
  errorDark: $errorDark;

  // orange
  orangeLight: $orangeLight;
  orangeMain: $orangeMain;
  orangeDark: $orangeDark;

  // warning
  warningLight: $warningLight;
  warningMain: $warningMain;
  warningDark: $warningDark;

  // grey
  grey50: $grey50;
  grey100: $grey100;
  grey200: $grey200;
  grey300: $grey300;
  grey500: $grey500;
  grey600: $grey600;
  grey700: $grey700;
  grey900: $grey900;

  // ==============================|| DARK THEME VARIANTS ||============================== //

  // paper & background
  darkPaper: $darkPaper;
  darkBackground: $darkBackground;

  // dark 800 & 900
  darkLevel1: $darkLevel1;
  darkLevel2: $darkLevel2;

  // text variants
  darkTextTitle: $darkTextTitle;
  darkTextPrimary: $darkTextPrimary;
  darkTextSecondary: $darkTextSecondary;
  darkTextDark: $darkTextDark;
  darkTableHeader: $darkTableHeader;

  // primary dark
  darkPrimaryLight: $darkPrimaryLight;
  darkPrimaryMain: $darkPrimaryMain;
  darkPrimaryDark: $darkPrimaryDark;
  darkPrimary200: $darkPrimary200;
  darkPrimary800: $darkPrimary800;

  // secondary dark
  darkSecondaryLight: $darkSecondaryLight;
  darkSecondaryMain: $darkSecondaryMain;
  darkSecondaryDark: $darkSecondaryDark;
  darkSecondary200: $darkSecondary200;
  darkSecondary800: $darkSecondary800;

  darkDivider: $darkDivider;
  darkSelectedBack: $darkSelectedBack;
  tableBackground: $tableBackground;
  tableBorderBottom: $tableBorderBottom;
}
