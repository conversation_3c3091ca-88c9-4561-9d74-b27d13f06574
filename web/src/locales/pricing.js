export const pricingTranslations = {
  zh: {
    'pricing_edit.unknown': '未知',
    'modelpricePage.tokens': '令牌',
    'modelpricePage.times': '次数',
    'modelpricePage.cached_tokens': '缓存令牌',
    'modelpricePage.cached_write_tokens': '缓存写入令牌',
    'modelpricePage.cached_read_tokens': '缓存读取令牌',
    'modelpricePage.input_audio_tokens': '音频输入令牌',
    'modelpricePage.output_audio_tokens': '音频输出令牌',
    'modelpricePage.reasoning_tokens': '推理令牌',
    'modelpricePage.input_text_tokens': '输入文本令牌',
    'modelpricePage.output_text_tokens': '输出文本令牌',
    'modelpricePage.input_image_tokens': '输入图像令牌',
    'modelpricePage.model': '模型',
    'modelpricePage.input': '输入价格',
    'modelpricePage.output': '输出价格',
    'modelpricePage.extraRatios': '额外倍率',
    'modelpricePage.noExtraRatios': '无额外倍率',
    'modelpricePage.all': '全部',
    'common.price': '基础价格',
    'common.all': '全部',
    'common.filter': '筛选',
    'common.search': '搜索',
    'common.pageSize': '每页条数',
    'common.noSearchResults': '没有找到匹配的结果',
    'common.noDataAvailable': '暂无数据',
    'common.actions': '操作',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.total': '总记录数',
    'pricing_edit.locked': '已锁定',
    'pricing_edit.unlocked': '未锁定'
  },
  en: {
    'pricing_edit.unknown': 'Unknown',
    'modelpricePage.tokens': 'Tokens',
    'modelpricePage.times': 'Times',
    'modelpricePage.cached_tokens': 'Cached Tokens',
    'modelpricePage.cached_write_tokens': 'Cached Write Tokens',
    'modelpricePage.cached_read_tokens': 'Cached Read Tokens',
    'modelpricePage.input_audio_tokens': 'Audio Input Tokens',
    'modelpricePage.output_audio_tokens': 'Audio Output Tokens',
    'modelpricePage.reasoning_tokens': 'Reasoning Tokens',
    'modelpricePage.input_text_tokens': 'Input Text Tokens',
    'modelpricePage.output_text_tokens': 'Output Text Tokens',
    'modelpricePage.input_image_tokens': 'Input Image Tokens',
    'modelpricePage.model': 'Model',
    'modelpricePage.input': 'Input Price',
    'modelpricePage.output': 'Output Price',
    'modelpricePage.extraRatios': 'Extra Ratios',
    'modelpricePage.noExtraRatios': 'No extra ratios',
    'modelpricePage.all': 'All',
    'common.price': 'Base Price',
    'common.all': 'All',
    'common.filter': 'Filter',
    'common.search': 'Search',
    'common.pageSize': 'Page Size',
    'common.noSearchResults': 'No matching results found',
    'common.noDataAvailable': 'No data available',
    'common.actions': 'Actions',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.total': 'Total',
    'pricing_edit.locked': 'Locked',
    'pricing_edit.unlocked': 'Unlocked'
  }
};
