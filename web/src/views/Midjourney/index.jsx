import { useState, useEffect, useCallback } from 'react';
import { showError, trims } from 'utils/common';

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import PerfectScrollbar from 'react-perfect-scrollbar';
import TablePagination from '@mui/material/TablePagination';
import LinearProgress from '@mui/material/LinearProgress';
import ButtonGroup from '@mui/material/ButtonGroup';
import Toolbar from '@mui/material/Toolbar';

import { Button, Card, Stack, Container, Typography, Box } from '@mui/material';
import LogTableRow from './component/TableRow';
import KeywordTableHead from 'ui-component/TableHead';
import TableToolBar from './component/TableToolBar';
import { API } from 'utils/api';
import { useIsAdmin } from 'utils/common';
import { PAGE_SIZE_OPTIONS, getPageSize, savePageSize } from 'constants';
import { Icon } from '@iconify/react';
import dayjs from 'dayjs';

import { useTranslation } from 'react-i18next'; // Add this import
export default function Log() {
  const { t } = useTranslation(); // Use i18n translations
  const originalKeyword = {
    p: 0,
    channel_id: '',
    mj_id: '',
    start_timestamp: 0,
    end_timestamp: dayjs().unix() * 1000 + 3600
  };

  const [page, setPage] = useState(0);
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('id');
  const [rowsPerPage, setRowsPerPage] = useState(() => getPageSize('midjourney'));
  const [listCount, setListCount] = useState(0);
  const [searching, setSearching] = useState(false);
  const [toolBarValue, setToolBarValue] = useState(originalKeyword);
  const [searchKeyword, setSearchKeyword] = useState(originalKeyword);
  const [refreshFlag, setRefreshFlag] = useState(false);

  const [logs, setLogs] = useState([]);
  const userIsAdmin = useIsAdmin();

  const handleSort = (event, id) => {
    const isAsc = orderBy === id && order === 'asc';
    if (id !== '') {
      setOrder(isAsc ? 'desc' : 'asc');
      setOrderBy(id);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setPage(0);
    setRowsPerPage(newRowsPerPage);
    savePageSize('midjourney', newRowsPerPage);
  };

  const searchLogs = async () => {
    // 如果正在搜索中，防止重复提交
    if (searching) {
      return;
    }

    setPage(0);
    // 使用时间戳来确保即使搜索条件相同也能触发重新搜索
    const searchPayload = {
      ...toolBarValue,
      _timestamp: Date.now()
    };
    setSearchKeyword(searchPayload);
  };

  const handleToolBarValue = (event) => {
    setToolBarValue({ ...toolBarValue, [event.target.name]: event.target.value });
  };

  const fetchData = useCallback(
    async (page, rowsPerPage, keyword, order, orderBy) => {
      setSearching(true);
      keyword = trims(keyword);

      // 移除仅用于触发状态更新的时间戳字段
      if (keyword._timestamp) {
        delete keyword._timestamp;
      }

      try {
        if (orderBy) {
          orderBy = order === 'desc' ? '-' + orderBy : orderBy;
        }
        const url = userIsAdmin ? '/api/mj/' : '/api/mj/self/';
        if (!userIsAdmin) {
          delete keyword.channel_id;
        }

        const res = await API.get(url, {
          params: {
            page: page + 1,
            size: rowsPerPage,
            order: orderBy,
            ...keyword
          }
        });
        const { success, message, data } = res.data;
        if (success) {
          setListCount(data.total_count);
          setLogs(data.data);
        } else {
          showError(message);
        }
      } catch (error) {
        console.error(error);
      }
      setSearching(false);
    },
    [userIsAdmin]
  );

  // 处理刷新
  const handleRefresh = async () => {
    setOrderBy('id');
    setOrder('desc');
    setToolBarValue(originalKeyword);
    setSearchKeyword(originalKeyword);
    setRefreshFlag(!refreshFlag);
  };

  useEffect(() => {
    fetchData(page, rowsPerPage, searchKeyword, order, orderBy);
  }, [page, rowsPerPage, searchKeyword, order, orderBy, fetchData, refreshFlag]);

  return (
    <>
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
        <Stack direction="column" spacing={1}>
          <Typography variant="h2">{t('midjourneyPage.midjourney')}</Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Midjourney
          </Typography>
        </Stack>
      </Stack>
      <Card>
        <Box component="form" noValidate>
          <TableToolBar filterName={toolBarValue} handleFilterName={handleToolBarValue} userIsAdmin={userIsAdmin} onSearch={searchLogs} />
        </Box>
        <Toolbar
          sx={{
            textAlign: 'right',
            height: 50,
            display: 'flex',
            justifyContent: 'space-between',
            p: (theme) => theme.spacing(0, 1, 0, 3)
          }}
        >
          <Container maxWidth="xl">
            <ButtonGroup variant="outlined" aria-label="outlined small primary button group">
              <Button onClick={handleRefresh} startIcon={<Icon icon="solar:refresh-circle-bold-duotone" width={18} />}>
                {t('midjourneyPage.refreshClearSearch')}
              </Button>

              <Button
                onClick={searchLogs}
                startIcon={
                  searching ? (
                    <Icon
                      icon="solar:refresh-bold-duotone"
                      width={18}
                      style={{
                        animation: 'spin 1s linear infinite',
                        color: '#1976d2'
                      }}
                    />
                  ) : (
                    <Icon icon="solar:minimalistic-magnifer-line-duotone" width={18} />
                  )
                }
                sx={{
                  ...(searching && {
                    bgcolor: 'action.hover',
                    color: 'primary.main',
                    '&:hover': {
                      bgcolor: 'action.selected'
                    }
                  })
                }}
              >
                {searching ? '搜索中...' : t('midjourneyPage.search')}
              </Button>
            </ButtonGroup>
          </Container>
        </Toolbar>
        {searching && <LinearProgress />}
        <PerfectScrollbar component="div">
          <TableContainer sx={{ overflow: 'unset' }}>
            <Table sx={{ minWidth: 800 }}>
              <KeywordTableHead
                order={order}
                orderBy={orderBy}
                onRequestSort={handleSort}
                headLabel={[
                  {
                    id: 'mj_id',
                    label: t('midjourneyPage.taskID'),
                    disableSort: false
                  },
                  {
                    id: 'submit_time',
                    label: t('midjourneyPage.submitTime'),
                    disableSort: false
                  },
                  {
                    id: 'channel_id',
                    label: t('midjourneyPage.channel'),
                    disableSort: false,
                    hide: !userIsAdmin
                  },
                  {
                    id: 'user_id',
                    label: t('midjourneyPage.user'),
                    disableSort: false,
                    hide: !userIsAdmin
                  },
                  {
                    id: 'action',
                    label: t('midjourneyPage.type'),
                    disableSort: false
                  },
                  {
                    id: 'code',
                    label: t('midjourneyPage.submissionResult'),
                    disableSort: false,
                    hide: !userIsAdmin
                  },
                  {
                    id: 'status',
                    label: t('midjourneyPage.taskStatus'),
                    disableSort: false,
                    hide: !userIsAdmin
                  },
                  {
                    id: 'progress',
                    label: t('midjourneyPage.progress'),
                    disableSort: true
                  },
                  {
                    id: 'time',
                    label: t('midjourneyPage.timeConsuming'),
                    disableSort: true
                  },
                  {
                    id: 'image_url',
                    label: t('midjourneyPage.resultImage'),
                    disableSort: true,
                    width: '120px'
                  },
                  {
                    id: 'prompt',
                    label: t('midjourneyPage.prompt'),
                    disableSort: true
                  },
                  {
                    id: 'prompt_en',
                    label: t('midjourneyPage.promptEn'),
                    disableSort: true
                  },
                  {
                    id: 'fail_reason',
                    label: t('midjourneyPage.failureReason'),
                    disableSort: true
                  }
                ]}
              />
              <TableBody>
                {logs.map((row, index) => (
                  <LogTableRow item={row} key={`${row.id}_${index}`} userIsAdmin={userIsAdmin} />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </PerfectScrollbar>
        <TablePagination
          page={page}
          component="div"
          count={listCount}
          rowsPerPage={rowsPerPage}
          onPageChange={handleChangePage}
          rowsPerPageOptions={PAGE_SIZE_OPTIONS}
          onRowsPerPageChange={handleChangeRowsPerPage}
          showFirstButton
          showLastButton
        />
      </Card>
    </>
  );
}
