---
title: "常见问题"
layout: doc
outline: deep
lastUpdated: true
---

# 常见问题

1. 额度是什么？怎么计算的？One API 的额度计算有问题？
   - 额度 = 分组倍率 _ 模型倍率 _ （提示 token 数 + 补全 token 数 \* 补全倍率）
   - 其中补全倍率对于 GPT3.5 固定为 1.33，GPT4 为 2，与官方保持一致。
   - 如果是非流模式，官方接口会返回消耗的总 token，但是你要注意提示和补全的消耗倍率不一样。
   - 注意，One API 的默认倍率就是官方倍率，是已经调整过的。
2. 账户额度足够为什么提示额度不足？
   - 请检查你的令牌额度是否足够，这个和账户额度是分开的。
   - 令牌额度仅供用户设置最大使用量，用户可自由设置。
3. 提示无可用渠道？
   - 请检查的用户分组和渠道分组设置。
   - 以及渠道的模型设置。
4. 渠道测试报错：`invalid character '<' looking for beginning of value`
   - 这是因为返回值不是合法的 JSON，而是一个 HTML 页面。
   - 大概率是你的部署站的 IP 或代理的节点被 CloudFlare 封禁了。
5. ChatGPT Next Web 报错：`Failed to fetch`
   - 部署的时候不要设置 `BASE_URL`。
   - 检查你的接口地址和 API Key 有没有填对。
   - 检查是否启用了 HTTPS，浏览器会拦截 HTTPS 域名下的 HTTP 请求。
6. 报错：`当前分组负载已饱和，请稍后再试`
   - 上游通道 429 了。
7. 升级之后我的数据会丢失吗？
   - 如果使用 MySQL，不会。
   - 如果使用 SQLite，需要按照我所给的部署命令挂载 volume 持久化 one-api.db 数据库文件，否则容器重启后数据会丢失。
8. 升级之前数据库需要做变更吗？
   - 一般情况下不需要，系统将在初始化的时候自动调整。
   - 如果需要的话，我会在更新日志中说明，并给出脚本。
