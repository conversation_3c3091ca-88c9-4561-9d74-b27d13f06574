# File path: /etc/systemd/system/one-api.service
# sudo systemctl daemon-reload
# sudo systemctl start one-api
# sudo systemctl enable one-api
# sudo systemctl status one-api
[Unit]
Description=One Hub Service
After=network.target

[Service]
User=ubuntu  # 注意修改用户名
WorkingDirectory=/path/to/one-api  # 注意修改路径
ExecStart=/path/to/one-api/one-api --port 3000 --log-dir /path/to/one-api/logs  # 注意修改路径和端口号
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
