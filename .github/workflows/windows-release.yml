name: Windows Release
permissions:
  contents: write

on:
  push:
    tags:
      - "*"
      - "!*-alpha*"
jobs:
  release:
    runs-on: windows-latest
    defaults:
      run:
        shell: bash
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 22.4.1
      - name: Build Frontend
        env:
          CI: ""
        run: |
          cd web
          npm install
          VITE_APP_VERSION=$(git describe --tags) npm run build
          cd ..
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.24.2"
      - name: Build Backend
        run: |
          go mod download
          go build -ldflags "-s -w -X 'done-hub/common/config.Version=$(git describe --tags)'" -o done-hub.exe
      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: done-hub.exe
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GT_Token }}
