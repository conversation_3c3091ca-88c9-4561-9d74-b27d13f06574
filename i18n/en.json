{"timestamp + key 做sha256, 再进行base64 encode": "sha256 with timestamp + key, then base64 encode", "去除最后一个/": "Remove the last /", "lint:ignore SA1029 reason: 需要使用该类型作为错误处理": "lint:ignore SA1029 reason: Need to use this type for error handling", "设置TCP超时": "Set TCP timeout", "从上下文中获取代理地址": "Get the proxy address from the context", "NewHTTPRequester 创建一个新的 HTTPRequester 实例。": "NewHTTPRequester creates a new HTTPRequester instance.", "proxyAddr: 是代理服务器的地址。": "proxyAddr: is the address of the proxy server.", "errorHandler: 是一个错误处理函数，它接收一个 *http.Response 参数并返回一个 *types.OpenAIErrorResponse。": "errorHandler: is an error handling function that takes a *http.Response parameter and returns a *types.OpenAIErrorResponse.", "如果 errorHandler 为 nil，那么会使用一个默认的错误处理函数。": "If errorHandler is nil, a default error handling function will be used.", "如果是以 socks5:// 开头的地址，那么使用 socks5 代理": "If the address starts with socks5://, use a socks5 proxy", "否则使用 http 代理": "Otherwise use an http proxy", "创建请求": "Create a request", "发送请求": "Send a request", "处理响应": "Handle the response", "解析响应": "Parse the response", "将响应体重新写入 resp.Body": "Rewrite the response body to resp.Body", "发送请求 RAW": "Send a request RAW", "获取流式响应": "Get a streaming response", "如果返回的头是json格式 说明有错误": "If the returned header is in json format, there is an error", "设置请求体": "Set the request body", "设置请求头": "Set the request header", "设置Content-Type": "Set the Content-Type", "判断是否为失败状态码": "Determine if it is a failure status code", "处理错误响应": "Handle the error response", "去除input前后空格": "Remove leading and trailing spaces from input", "查询该tg用户是否已经绑定其他账户": "Query whether the tg user has already bound another account", "绑定": "Link", "去除/": "Remove /", "追加自定义菜单": "Append custom menu", "菜单 1. 绑定 2. 解绑 3. 查询余额 4. 充值 5. 获取API_KEY": "Menu 1. Link 2. Unlink 3. Check balance 4. Recharge 5. Get API_KEY", "根据总数计算总页数": "Calculate the total number of pages based on the total number", "check choices 长度大于1": "check choices length greater than 1", "返回第一个错误字段的名称": "Return the name of the first error field", "处理 ImageRequest": "Handle ImageRequest", "处理 ImageEditsRequest": "Handle ImageEditsRequest", "创建一个 http.Request": "Create an http.Request", "转换为JSON字符串": "Convert to JSON string", "通道为禁用状态，并且还是请求错误 或者 响应时间超过阈值 直接跳过，也不需要更新响应时间。": "The channel is disabled, and it is a request error or the response time exceeds the threshold, so skip it, and there is no need to update the response time.", "如果已被禁用，但是请求成功，需要判断是否需要恢复": "If it has been disabled, but the request is successful, it needs to be judged whether it needs to be restored", "手动禁用的通道，不会自动恢复": "Manually disabled channels will not be restored automatically", "如果通道启用状态，但是返回了错误 或者 响应时间超过阈值，需要判断是否需要禁用": "If the channel is enabled, but an error is returned or the response time exceeds the threshold, it needs to be judged whether it needs to be disabled", "如果没有未完成的任务，则等待": "If there are no unfinished tasks, wait", "统计失败的未完成任务": "Statistics of failed unfinished tasks", "设置超时时间": "Set the timeout", "使用带有超时的 context 创建新的请求": "Create a new request with a context with a timeout", "如果时间超过一小时，且进度不是100%，则认为任务失败": "If the time exceeds one hour and the progress is not 100%, the task is considered failed", "遍历， 禁止有相同的command": "Traverse, prohibit the same command", "获取7天前 00:00:00 和 今天23:59:59  的秒时间戳": "Get the second timestamp of 00:00:00 7 days ago and 23:59:59 today", "添加删除cache的任务": "Add delete cache task", "如果是以 *结尾的 model名称": "If the model name ends with *", "逗号分割 ability.ChannelId": "Comma-separated ability.ChannelId", "同时删除Ability": "Delete Ability at the same time", "获取当前时间戳": "Get the current timestamp", "获取总数": "Get the total number", "分页": "Pagination", "排序": "Sorting", "默认排序": "Default sorting", "查询": "Query", "返回结果": "Return results", "如果是主键，移除主键约束": "If it is a primary key, remove the primary key constraint", "修改字段长度": "Modify the field length", "TaskQueryParams 用于包含所有搜索条件的结构体，可以根据需求添加更多字段": "TaskQueryParams is a structure that contains all search criteria and can add more fields as needed", "初始化查询构建器": "Initialize the query builder", "假设您已将前端传来的时间戳转换为数据库所需的时间格式，并处理了时间戳的验证和解析": "Assuming you have converted the timestamp passed by the front end into the time format required by the database, and handled the verification and parsing of the timestamp", "添加过滤条件": "Add filter conditions", "查询菜单列表  只查询command和description": "Query the menu list only command and description", "根据command查询菜单": "Query the menu based on command", "获取状态为可用的令牌": "Get tokens that are available", "防止Redis缓存不生效，直接删除": "Prevent Redis cache from not taking effect, delete directly", "不改变当前数据库索引，通过更改用户名来删除用户": "Do not change the current database index, delete the user by changing the username", "定义供应商工厂": "Define a supplier factory", "创建 AliProvider": "C<PERSON> <PERSON>", "请求错误处理": "Request error handling", "错误处理": "Error handling", "获取请求头": "Get request header", "转换成JSON字符串": "Convert to JSON string", "打印 context 写入的内容": "Print the content written to the context", "检测头部是否有X-DashScope-SSE: enable": "Check if the header has X-DashScope-SSE: enable", "以换行符分割response": "Split response by newline", "如果最后一行为空，则删除最后一行": "If the last line is empty, delete the last line", "循环遍历每一行": "Loop through each line", "assert判断 是否以data: 开头": "assert judgment whether it starts with data:", "检测最后一行是否以data: [DONE] 结尾": "Check if the last line ends with data: [DONE]", "检测倒数第二行是否存在": "Check if the penultimate line exists", "获取请求地址": "Get the request address", "转换为OpenAI聊天请求体": "Convert to OpenAI chat request body", "阿里云聊天请求体": "<PERSON>yun chat request body", "检测是否开启了 web_search 插件": "Check if the web_search plugin is enabled", "转换为OpenAI聊天流式请求体": "Convert to OpenAI chat streaming request body", "如果rawLine 前缀不为data:，则直接返回": "If the rawLine prefix is not data:, return directly", "去除前缀": "Remove the prefix", "创建 AzureProvider": "Create AzureProvider", "创建 AzureSpeechProvider": "Create AzureSpeechProvider", "检测是否错误": "Check if there is an error", "休眠 2 秒": "Sleep for 2 seconds", "创建 BaichuanProvider": "Create <PERSON><PERSON><PERSON>", "获取聊天请求体": "Get chat request body", "创建 BaiduProvider": "C<PERSON> <PERSON><PERSON>", "获取完整请求 URL": "Get the complete request URL", "获取基础URL": "Get the base URL", "获取完整请求URL": "Get the complete request URL", "基础接口": "Basic interface", "获取用量": "Get usage", "设置用量": "Set usage", "设置Context": "Set Context", "设置原始模型": "Set the original model", "获取原始模型": "Get the original model", "完成接口": "Complete interface", "聊天接口": "Chat interface", "嵌入接口": "Embed interface", "审查接口": "Review interface", "文字转语音接口": "Text-to-speech interface", "语音转文字接口": "Speech-to-text interface", "语音翻译接口": "Voice translation interface", "图片生成接口": "Image generation interface", "图片编辑接口": "Image editing interface", "余额接口": "Balance interface", "响应处理函数": "Response processing function", "点分割": "Point split", "删除model字段": "Delete model field", "创建 BedrockProvider": "Create BedrockProvider", "创建 ClaudeProvider": "<PERSON><PERSON> <PERSON>", "创建 CloudflareAIProvider": "Create CloudflareAIProvider", "如果rawLine 前缀不为data: 或者 meta:，则直接返回": "If the rawLine prefix is not data: or meta:, return directly", "可以是 string 或者 ImageObject": "Can be a string or ImageObject", "可以是 string 或者 MaskObject": "Can be a string or MaskObject", "创建 CohereProvider": "Create <PERSON>here<PERSON>", "创建 DeepseekProvider": "Create <PERSON><PERSON><PERSON>", "创建 GeminiProvider": "Create <PERSON>", "开始判断": "Start judgment", "创建 GroqProvider": "Create GroqProvider", "创建 LingyiProvider": "C<PERSON> <PERSON>", "创建 MidjourneyProvider": "Create MidjourneyProvider", "创建 MiniMaxProvider": "Create MiniMaxProvider", "如果role为function， 则需要在前面一条记录添加function_call，如果没有消息，则添加一个message": "If the role is function, you need to add function_call to the previous record, and if there is no message, add a message", "创建 MistralProvider": "Create <PERSON><PERSON><PERSON>", "循环Loc，拼接成字符串": "Loop Loc and concatenate into a string", "返回字符串": "Return a string", "创建 MoonshotProvider": "Create <PERSON><PERSON>", "创建 OpenAIProvider": "Create OpenAIProvider", "检测模型是是否包含 . 如果有则直接去掉": "Check if the model contains . If so, remove it directly", "因为dall-e-3需要api-version=2023-12-01-preview，但是该版本": "Because dall-e-3 requires api-version=2023-12-01-preview, but this version", "已经没有dall-e-2了，所以暂时写死": "There is no dall-e-2 anymore, so it is temporarily hardcoded", "那么需要计算": "Then you need to calculate", "如果等于 DONE 则结束": "If it is equal to DONE, it ends", "阻塞 20ms": "Block 20ms", "请求流处理函数": "Request stream processing function", "创建 PalmProvider": "Create <PERSON>", "创建 StabilityAIProvider": "Create StabilityAIProvider", "创建 TencentProvider": "Create Tencent<PERSON>rov<PERSON>", "腾讯云账号的 APPID": "Tencent Cloud account APPID", "官网 SecretId": "Official website SecretId", "Timestamp当前 UNIX 时间戳，单位为秒，可记录发起 API 请求的时间。": "Timestamp current UNIX timestamp in seconds, which can record the time when the API request was initiated.", "例如**********，如果与当前时间相差过大，会引起签名过期错误": "For example, **********, if the difference from the current time is too large, it will cause a signature expiration error", "Expired 签名的有效期，是一个符合 UNIX Epoch 时间戳规范的数值，": "Expired The validity period of the signature is a value that conforms to the UNIX Epoch timestamp specification,", "单位为秒；Expired 必须大于 Timestamp 且 Expired-Timestamp 小于90天": "in seconds; Expired must be greater than Timestamp and Expired-Timestamp less than 90 days", "请求 Id，用于问题排查": "Request Id, used for problem troubleshooting", "Temperature 较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定": "A higher value of Temperature will make the output more random, while a lower value will make it more concentrated and determined", "默认 1.0，取值区间为[0.0,2.0]，非必要不建议使用,不合理的取值会影响效果": "Default 1.0, the value range is [0.0,2.0], it is not recommended to use it if it is not necessary, unreasonable values will affect the effect", "建议该参数和 top_p 只设置1个，不要同时更改 top_p": "It is recommended that only one of this parameter and top_p be set, and do not change top_p at the same time", "TopP 影响输出文本的多样性，取值越大，生成文本的多样性越强": "TopP affects the diversity of the output text. The larger the value, the stronger the diversity of the generated text", "默认1.0，取值区间为[0.0, 1.0]，非必要不建议使用, 不合理的取值会影响效果": "Default 1.0, the value range is [0.0, 1.0], it is not recommended to use it if it is not necessary, unreasonable values will affect the effect", "建议该参数和 temperature 只设置1个，不要同时更改": "It is recommended that only one of this parameter and temperature be set, and do not change it at the same time", "Stream 0：同步，1：流式 （默认，协议：SSE)": "Stream 0: synchronous, 1: streaming (default, protocol: SSE)", "同步请求超时：60s，如果内容较长建议使用流式": "Synchronous request timeout: 60s, if the content is long, it is recommended to use streaming", "Messages 会话内容, 长度最多为40, 按对话时间从旧到新在数组中排列": "Messages Session content, up to 40 in length, arranged in an array from old to new according to the conversation time", "输入 content 总数最大支持 3000 token。": "The maximum number of input content tokens is 3000.", "流式结束标志位，为 stop 则表示尾包": "Streaming end flag, stop means tail packet", "内容，同步模式返回内容，流模式为 null 输出 content 内容总数最多支持 1024token。": "Content, synchronous mode returns content, streaming mode is null Output content content supports up to 1024 tokens.", "内容，流模式返回内容，同步模式为 null 输出 content 内容总数最多支持 1024token。": "Content, streaming mode returns content, synchronous mode is null Output content content supports up to 1024 tokens.", "结果": "Result", "unix 时间戳的字符串": "unix timestamp string", "会话 id": "Session id", "token 数量": "Token count", "注释": "Note", "唯一请求 Id，每次请求都会返回。用于反馈接口入参": "Unique request ID, which will be returned for each request. Used to feedback interface parameters", "模型名称": "Model Name", "创建 XunfeiProvider": "Create <PERSON><PERSON><PERSON>", "创建 ZhipuProvider": "C<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "检测 topP 是否在 0-1 之间 如果等于0 设为0.1 如果大于等于1 设为0.9": "Check if topP is between 0-1 If it is equal to 0, set it to 0.1 If it is greater than or equal to 1, set it to 0.9", "如果有图片的话，并且是base64编码的图片，需要把前缀去掉": "If there is a picture, and it is a base64 encoded picture, you need to remove the prefix", "检测是否开启了 retrieval 插件": "Check if the retrieval plugin is enabled", "如果开启了 retrieval 插件，web_search 无效": "If the retrieval plugin is enabled, web_search is invalid", "定义供应商工厂接口": "Define the supplier factory interface", "创建全局的供应商工厂映射": "Create a global supplier factory map", "在程序启动时，添加所有的供应商工厂": "Add all supplier factories when the program starts", "获取供应商": "Get the supplier", "处理未找到的供应商工厂": "Handle unfound supplier factory", "从Content-Type头获取MIME类型": "Get the MIME type from the Content-Type header", "如果无法确定内容类型，则默认为jpeg": "If the content type cannot be determined, it defaults to jpeg", "设置响应的内容类型": "Set the content type of the response", "将图片流式传输到响应体": "Stream the image to the response body", "开始激活任务": "Start activating tasks", "midjourney plus，需要从customId中获取任务信息": "midjourney plus, need to get task information from customId", "绘画任务，此类任务可重复": "Drawing tasks, such tasks can be repeated", "按图生文任务，此类任务可重复": "Text-to-image tasks, such tasks can be repeated", "缩短任务，此类任务可重复，plus only": "Shortening tasks, such tasks can be repeated, plus only", "放大、变换任务，此类任务，如果重复且已有结果，远端api会直接返回最终结果": "Enlargement and transformation tasks, such tasks, if repeated and already have results, the remote api will directly return the final result", "原任务的Status=SUCCESS，则可以做放大UPSCALE、变换VARIATION等动作，此时必须使用原来的请求地址才能正确处理": "If the original task's Status=SUCCESS, you can perform actions such as enlargement UPSCALE, transformation VARIATION, etc., and you must use the original request address to process it correctly", "文档：https://github.com/novicezk/midjourney-proxy/blob/main/docs/api.md": "Document: https://github.com/novicezk/midjourney-proxy/blob/main/docs/api.md", "1-提交成功": "1-Submitted successfully", "21-任务已存在（处理中或者有结果了）": "21-The task already exists (being processed or has results)", "任务已存在": "The task already exists", "22-排队中": "22-In the queue", "排队中，前面还有1个任务": "In the queue, there is still 1 task in front", "23-队列已满，请稍后再试": "23-The queue is full, please try again later", "队列已满，请稍后尝试": "The queue is full, please try again later", "24-prompt包含敏感词": "24-prompt contains sensitive words", "可能包含敏感词": "May contain sensitive words", "other: 提交错误，description为错误描述": "other: Submission error, description is the error description", "非1-提交成功,21-任务已存在和22-排队中，则记录错误原因": "If it is not 1-Submitted successfully, 21-The task already exists, and 22-In the queue, record the error reason", "将 properties 转换为一个 map": "Convert properties to a map", "修改返回值": "Modify the return value", "22-排队中，说明任务已存在": "22-In the queue, indicating that the task already exists", "初始化时，需要检测是否有更新": "When initializing, you need to check if there are updates", "查找需要删除的model": "Find the model to be deleted", "如果输入费率和输出费率一样，则只显示一个费率": "If the input rate and output rate are the same, only one rate is displayed", "如果没有报错，则消费配额": "If there is no error, consume the quota", "将data转换为 JSON": "Convert data to JSON", "检查是否是 data: 开头的流式数据": "Check if it is streaming data starting with data:", "获取缓存": "Get the cache", "说明有缓存， 直接返回缓存内容": "Indicates that there is a cache, return the cache content directly", "冻结通道": "Freeze channel", "写入日志": "Write to log", "根据 OwnedBy 排序": "Sort by OwnedBy", "假设 nil 值小于任何非 nil 值": "Assume that a nil value is less than any non-nil value", "假设任何非 nil 值大于 nil 值": "Assume that any non-nil value is greater than nil value", "转换为JSON": "Convert to JSON", "取第一个": "Take the first one", "创建 CozeProvider": "<PERSON><PERSON><PERSON>", "请求失败，设置错误信息": "Request failed, set error information", "继承 MuiPopover-root": "In<PERSON><PERSON><PERSON>-root", "将 date设置当天的 00:00:00": "Set the date to 00:00:00 of the current day", "将 date设置当天的 23:59:59": "Set the date to 23:59:59 of the current day", "添加 id 的验证": "Add id verification", "添加 message 的验证": "Add message verification", "格式化数值": "Format the value", "如果大于1000，显示为k": "If greater than 1000, display as k", "合并 options 和 snackbarConstants.Mobile": "Merge options and snackbarConstants.Mobile", "获取每天所有渠道的平均延迟": "Get the average delay of all channels every day", "追加latency列表后面": "Append to the end of the latency list", "// base_url 是必需的": "// base_url is required", "// 在其他情况下，base_url 可以是任意字符串": "// In other cases, base_url can be any string", "处理插件事务": "Handle plugin transactions", "循环 modelOptions，找到 modelGroup 对应的模型": "Loop through modelOptions to find the model corresponding to modelGroup", "先对data排序": "Sort data first", "// 如果 channelModel 是一个字符串": "// If channelModel is a string", "新页面打开": "Open in new page", "删除 data.id": "Delete data.id", "处理刷新": "Handle refresh", "处理测试所有启用渠道": "Handle testing all enabled channels", "处理删除所有禁用渠道": "Handle deleting all disabled channels", "处理更新所有启用渠道余额": "Handle updating the balance of all enabled channels", "对 res.data.data 里面的 owned_by 进行分组": "Group the owned_by in res.data.data", "检测是否是一个列表": "Check if it is a list", "判断 type 是否是 等于 tokens || times": "Determine if type is equal to tokens || times", "判断 model是否是唯一值": "Determine if the model is a unique value", "执行删除操作": "Perform delete operation", "如果版本前缀是v开头的": "If the version prefix starts with v", "设置默认模型为 gpt-3.5-turbo": "Set the default model to gpt-3.5-turbo", "导出到当前目录下的 prices.json 文件": "Export to prices.json file in the current directory", "SMTP 信息未配置": "SMTP information is not configured", "您正在进行密码重置。点击下方按钮以重置密码。": "You are resetting your password. Click the button below to reset your password.", "重置密码": "Reset Password", "如果链接无法点击，请尝试点击下面的链接或将其复制到浏览器中打开：": "If the link cannot be clicked, please try to click the link below or copy it to the browser to open:", "重置链接 %d 分钟内有效，如果不是本人操作，请忽略。": "The reset link is valid within %d minutes. If it is not your operation, please ignore it.", "%s密码重置": "%s Password Reset", "您正在进行邮箱验证。您的验证码为: ": "You are verifying your email. Your verification code is: ", "验证码 %d 分钟内有效，如果不是本人操作，请忽略。": "The verification code is valid within %d minutes. If it is not your operation, please ignore it.", "%s邮箱验证邮件": "%s Email Verification Email", "%s，当前剩余额度为 %d，为了不影响您的使用，请及时充值。": "%s, the current remaining quota is %d, in order not to affect your use, please recharge in time.", "点击充值": "Click to recharge", "您的额度即将用尽": "Your balance is about to run out", "您的额度已用尽": "Your balance has been used up", "系统错误，请稍后再试": "System error, please try again later", "您可以通过分享您的邀请码来邀请朋友，每次成功邀请将获得奖励。\n\n您的邀请码是:": "You can invite friends by sharing your invitation code, and you will receive rewards for each successful invitation.\n\nYour invitation code is:", "\n\n页面地址：": "\n\nPage address:", "找不到令牌": "Token not found", "点击令牌可复制": "Click the token to copy", "<b>余额：</b> $%s \n<b>已用：</b> $%s": "<b>Balance:</b> $%s \n<b>Used:</b> $%s", "您的账户已绑定，请解邦后再试": "Your account is already linked, please unlink and try again", "请输入你的访问令牌": "Please enter your access token", "Token 错误，请重试": "Token error, please try again", "该TG已绑定其他账户，请解邦后再试": "This telegram account is already linked to another account, please unlink and try again", "绑定失败，请稍后再试": "Telegram linking failed, please try again later", "绑定成功": "Telegram linking succeeded", "未找到该命令": "Command not found", "请输入你的兑换码": "Please enter your redemption code", "充值失败": "Recharge failed", "成功充值 $%s ": "Successfully recharged $%s ", "解邦成功": "Telegram unlink succeeded", "绑定账号": "Link Account", "解绑账号": "Unlink Account", "查询余额": "Check Balance", "充值": "Billing", "获取API_KEY": "Get API_KEY", "获取邀请链接": "Get Invitation Link", "无法使用命令": "Unable to use command", "您的账户未绑定": "Your account is not linked", "取消": "Cancel", "已取消!": "Cancelled!", "参数错误!": "Parameter error!", "未知的类型!": "Unknown type!", "上一页(%d/%d)": "Previous(%d/%d)", "下一页(%d/%d)": "Next(%d/%d)", "＄%.6f 额度": "＄%.6f quota", "%d 点额度": "%d point quota", " 秒": "s", " 分钟 ": " m ", " 小时 ": " h ", " 天 ": " d ", " 个月 ": " M ", " 年 ": " y ", "无法获取用户统计信息.": "Unable to get user statistics.", "无法获取渠道统计信息.": "Unable to get channel statistics.", "无法获取充值卡统计信息.": "Unable to get redemption card statistics.", "无法获取用户区间统计信息.": "Unable to get user interval statistics.", "无法获取渠道区间统计信息.": "Unable to get channel interval statistics.", "无法获取充值区间统计信息.": "Unable to get recharge interval statistics.", "余额不足": "Insufficient balance", "请填写测速模型后再试": "Please fill in the test model and try again", "测试渠道 %s : %s 返回内容为：%s": "Test channel %s : %s returns content: %s", "测试已在运行中": "Test is already running", "**通道 %s - #%d - %s**": "**Channel %s - #%d - %s**", "- 测试报错: %s \n\n- 无需改变状态，跳过\n\n": "- Test error: %s \n\n- No need to change the status, skip\n\n", "- 响应时间 %.2fs 超过阈值 %.2fs \n\n- 无需改变状态，跳过\n\n": "- Response time %.2fs exceeds threshold %.2fs \n\n- No need to change the status, skip\n\n", "- 已被启用 \n\n": "- Has been enabled \n\n", "- 手动禁用的通道，不会自动恢复 \n\n": "- Manually disabled channels will not be restored automatically \n\n", "- 响应时间 %.2fs 超过阈值 %.2fs \n\n- 禁用\n\n": "- Response time %.2fs exceeds threshold %.2fs \n\n- Disabled\n\n", "- 已被禁用，原因：%s\n\n": "- Disabled, reason: %s\n\n", "- 测试报错: %s \n\n": "- Test error: %s \n\n", "- 测试完成，耗时 %.2fs\n\n": "- Test completed, time consumed %.2fs\n\n", "通道测试完成": "Channel test completed", "ids不能为空": "ids cannot be empty", "更新成功": "Update successful", "通道「%s」（#%d）已被禁用": "Channel %s (#%d) has been disabled", "通道「%s」（#%d）已被禁用，原因：%s": "Channel %s (#%d) has been disabled, reason: %s", "通道「%s」（#%d）已被启用": "Channel %s (#%d) has been enabled", "无效的参数": "Invalid parameter", "无法连接至 GitHub 服务器，请稍后重试！": "Unable to connect to GitHub server, please try again later!", "返回值非法，用户字段为空，请稍后重试！": "The return value is illegal, the user field is empty, please try again later!", "管理员未开启通过 GitHub 登录以及注册": "The administrator did not turn on login and registration via GitHub", "管理员关闭了新用户注册": "The administrator has turned off new user registration", "用户已被封禁": "User has been banned", "该 GitHub 账户已被绑定": "The GitHub account has been linked", "无法连接至飞书服务器，请稍后重试！": "Unable to connect to the Feishu server, please try again later!", "该飞书账户已被绑定": "The Feishu account has been linked!", "检测到未完成的任务数有: %v": "Detected unfinished task count: %v", "渠道 #%d 未完成的任务有: %d": "Channel #%d has %d unfinished tasks", "获取渠道信息失败，请联系管理员，渠道ID：%d": "Failed to get channel information, please contact the administrator, channel ID: %d", "上游任务超时（超过1小时）": "Upstream task timeout (more than 1 hour)", " 构建失败，": " build failed, ", "构图失败 %s，补偿 %s": "Graph construction failed %s, compensation %s", "管理员启用了邮箱域名白名单，您的邮箱地址的域名不在白名单中": "The administrator has enabled the email domain whitelist, and the domain name of your email address is not in the whitelist", "邮箱地址已被占用": "Email address is already in use", "该邮箱地址未注册": "The email address is not registered", "重置链接非法或已过期": "Reset link is illegal or expired", "无法启用 GitHub OAuth，请先填入 GitHub Client ID 以及 GitHub Client Secret！": "Unable to enable GitHub OAuth, please fill in GitHub Client ID and GitHub Client Secret first!", "无法启用邮箱域名限制，请先填入限制的邮箱域名！": "Unable to enable email domain restriction, please fill in the restricted email domain first!", "无法启用微信登录，请先填入微信登录相关配置信息！": "Unable to enable WeChat login, please fill in the relevant configuration information for WeChat login first!", "无法启用 Turnstile 校验，请先填入 Turnstile 校验相关配置信息！": "Unable to enable Turnstile verification, please fill in the relevant configuration information for Turnstile verification first!", "兑换码名称长度必须在1-20之间": "The length of the redemption code name must be between 1-20", "兑换码个数必须大于0": "The number of redemption codes must be greater than 0", "一次兑换码批量生成的个数不能大于 100": "The number of redemption codes generated in a batch cannot be greater than 100", "command已存在": "Command already exists", "添加成功": "Added successfully", "修改成功": "Updated successfully", "删除成功": "Deleted successfully", "重载成功": "Reloaded successfully", "令牌名称过长": "Token name is too long", "令牌已过期，无法启用，请先修改令牌过期时间，或者设置为永不过期": "The token has expired and cannot be enabled. Please modify the expiration time of the token, or set it to never expire.", "令牌可用额度已用尽，无法启用，请先修改令牌剩余额度，或者设置为无限额度": "The available quota of the token has been used up and cannot be enabled. Please modify the remaining quota of the token, or set it to unlimited quota", "管理员关闭了密码登录": "The administrator has turned off password login", "无法保存会话信息，请重试": "Unable to save session information, please try again", "管理员关闭了通过密码进行注册，请使用第三方账户验证的形式进行注册": "The administrator has turned off registration via password. Please use the form of third-party account verification to register", "输入不合法 ": "Illegal input ", "管理员开启了邮箱验证，请输入邮箱地址和验证码": "The administrator has turned on email verification, please enter the email address and verification code", "验证码错误或已过期": "Verification code error or expired", "无权获取同级或更高等级用户的信息": "No permission to get information of users at the same level or higher", "无法获取统计信息": "Unable to get statistics", "请重试，系统生成的 UUID 竟然重复了！": "Please try again, the system-generated UUID is actually duplicated!", "无权更新同权限等级或更高权限等级的用户信息": "No permission to update user information with the same permission level or higher permission level", "无权将其他用户权限等级提升到大于等于自己的权限等级": "No permission to Promote other user privilege levels to a privilege level greater than or equal to your own", "管理员将用户额度从 %s修改为 %s": "The administrator changed the user quota from %s to %s", "无权删除同权限等级或更高权限等级的用户": "No permission to delete users with the same permission level or higher permission level", "不能删除超级管理员账户": "Cannot delete super administrator account", "无法创建权限大于等于自己的用户": "Unable to create users with permissions greater than or equal to your own", "用户不存在": "User does not exist", "无法禁用超级管理员用户": "Unable to disable super administrator user", "无法删除超级管理员用户": "Unable to delete super administrator user", "普通管理员用户无法提升其他用户为管理员": "Ordinary administrator users cannot promote other users to administrators", "该用户已经是管理员": "The user is already an administrator", "无法降级超级管理员用户": "Unable to downgrade super administrator user", "该用户已经是普通用户": "The user is already an ordinary user", "管理员未开启通过微信登录以及注册": "The administrator has not enabled login and registration via WeChat", "该微信账号已被绑定": "The WeChat account has been linked", "删除过期缓存数据": "Delete expired cache data", "无权进行此操作，未登录且未提供 access token": "No permission to perform this operation, not logged in and no access token provided", "无权进行此操作，access token 无效": "No permission to perform this operation, access token is invalid", "无权进行此操作，权限不足": "No permission to perform this operation, insufficient permissions", "无效的渠道 ID": "Invalid channel ID", "普通用户不支持指定渠道": "Ordinary users do not support specifying channels", "Turnstile token 为空": "Turnstile token is empty", "Turnstile 校验失败，请刷新重试！": "Turnstile verification failed, please refresh and try again!", "启用": "Enable", "自动禁用": "Automatically disabled", "手动禁用": "Manually disabled", "禁用": "Disable", "size 参数不能超过 %d": "The size parameter cannot exceed %d", "不允许对字段 '%s' 进行排序": "Sorting on field '%s' is not allowed", "id 为空！": "id is empty!", "未提供兑换码": "No redemption code provided", "无效的 user id": "Invalid user id", "无效的兑换码": "Invalid redemption code", "该兑换码已被使用": "The redemption code has been used", "兑换失败，": "Redemption failed, ", "通过兑换码充值 %s": "Recharge %s through redemption code", "未提供令牌": "No token provided", "无效的令牌": "Invalid token", "令牌验证失败": "Token verification failed", "该令牌额度已用尽": "You exceeded your current quota, please check your plan and billing details or token's quota.", "该令牌已过期": "The token has expired", "该令牌状态不可用": "The token status is not available", "id 或 userId 为空！": "id or userId is empty!", "quota 不能为负数！": "quota cannot be negative!", "令牌额度不足": "Insufficient token quota", "用户额度不足": "Insufficient user quota", "telegramId 为空！": "telegramId is empty!", "affCode 为空！": "affCode is empty!", "用户名已存在！": "Username already exists!", "新用户注册赠送 %s": "New user registration gives %s", "使用邀请码赠送 %s": "Use invitation code to give %s", "邀请用户赠送 %s": "Invite users to give %s", "用户名或密码为空": "Username or password is empty", "用户名或密码错误，或用户已被封禁": "Username or password is wrong, or user has been banned", "email 为空！": "email is empty!", "GitHub id 为空！": "GitHub id is empty!", "WeChat id 为空！": "WeChat id is empty!", "lark id 为空！": "lark id is empty!", "username 为空！": "username is empty!", "邮箱地址或密码为空！": "Email address or password is empty!", "这是函数调用返回的内容，请回答之前的问题：\n": "This is the content returned by the function call, please answer the previous question:\n", "不支持余额查询": "Balance inquiry is not supported", "检测到此操作为放大、变换、重绘，获取原channel信息: %d": "Detected that this operation is enlargement, transformation, and redrawing, get the original channel information: %d", "无效的请求, 无法解析模型": "Invalid request, unable to resolve the model", "无法获取provider:": "Unable to get provider:", "无效的请求, 无法获取midjourney provider": "Invalid request, unable to get midjourney provider", "当前分组负载已饱和，请稍后再试，或升级账户以提升服务质量。": "The current group load is saturated, please try again later, or upgrade your account to improve service quality.", "$%g/次": "$%g/time", "$%g/1k (输入) | $%g/1k (输出)": "$%g/1k (input) | $%g/1k (output)", "模型费率 %s，分组倍率 %.2f": "Model rate %s, group rate %.2f", " 未知 ": " Unknown ", "未知": "Unknown", "无效的渠道 Id": "Invalid channel Id", "该渠道已被禁用": "The channel has been disabled", "当前分组 %s 下对于模型 %s 无可用渠道": "There are no available channels for model %s under the current group %s", "渠道不存在：%d": "Channel does not exist: %d", "数据库一致性已被破坏，请联系管理员": "Database consistency has been broken, please contact the administrator", "当前分组上游负载已饱和，请稍后再试": "The current group load is saturated, please try again later", "未知版本": "Unknown version", "dayjs/locale/zh-cn": "dayjs/locale/en", "zh-cn": "en", "IconCoinYuan": "IconCoin", "All in one 的 OpenAI 接口": "All-in-one OpenAI interface", "整合各种 API 访问方式": "Integrate various API access methods", "一键部署，开箱即用": "One-click deployment, ready to use", "可在设置页面设置关于内容，支持 HTML & Markdown": "You can set the content on the settings page, support HTML & Markdown", "项目仓库地址：": "Project repository address:{' '}", "由{' '}": "By{' '}", "开发，基于": "developed based on", "Done Hub": "Done Hub. Source code follows", "MIT 协议": "MIT License", "OpenAI 接口聚合管理，支持多种渠道包括 Azure，可用于二次分发管理 key，仅单可执行文件，已打包好 Docker 镜像，一键部署，开箱即用": "OpenAI interface aggregation management. Supports multiple channels including Azure. Can be used for secondary distribution management key. Only single executable file, packaged Docker image, one-click deployment, ready to use", "百度文心千帆": "<PERSON><PERSON>", "阿里通义千问": "Alibaba <PERSON>", "讯飞星火认知": "Xunfei Starfire Cognitive", "智谱 ChatGLM": "Zhipu ChatGLM", "360 智脑": "360 Brain", "腾讯混元": "<PERSON>cent <PERSON>", "百川": "Baichuan", "零一万物": "Zero One Everything", "自定义渠道": "Custom Channel", "新版本可用：${data.version}，请使用快捷键 Shift + F5 刷新页面": "New version available: ${data.version}, please use the shortcut key Shift + F5 to refresh the page", "无法正常连接至服务器！": "Unable to connect to the server normally!", "绑定成功！": "Account successfully linked!", "登录成功！": "<PERSON><PERSON> succeeded!", "注册成功！": "Registration succeeded!", "验证码发送成功，请检查你的邮箱！": "Verification code sent successfully, please check your email!", "设置": "Settings", "注销": "Logout", "欢迎回来": "Welcome back", "首页": "Home", "关于": "About", "控制台": "<PERSON><PERSON><PERSON>", "登录": "<PERSON><PERSON>", "统计": "Statistics", "暂无数据": "No data", "无数据": "No data", "错误：请求次数过多，请稍后再试！": "Error: Too many requests, please try again later!", "错误：服务器内部错误，请联系管理员！": "Error: Internal server error, please contact the administrator!", "本站仅作演示之用，无服务端！": "This site is for demonstration only, no server!", "错误：": "Error: ", "复制${name}失败，请手动复制：<br /><br />${text}": "Failed to copy ${name}, please copy it manually:<br /><br />${text}", "复制${name}成功！": "Copied ${name} successfully!", "（等价金额：${renderQuota(quota, digits)}）": "(Equivalent amount: ${renderQuota(quota, digits)})", "消费统计": "Consumption statistics", "Tokens统计": "Tokens statistics", "平均延迟": "Average latency", "请求数": "Number of requests", "兑换统计": "Redemption statistics", "注册统计": "Registration statistics", "美元": "USD", "总消费：$": "Total consumption: $", "总Tokens：": "Total Tokens: ", "次": "times", "总请求数：": "Total number of requests: ", "秒": "s", "兑换金额($)": "Redemption amount($)", "独立用户(人)": "Unique users", "直接注册": "Direct registration", "邀请注册": "Invitation registration", "人": "people", "总注册人数：": "Total number of registrations: ", "用户总消费金额": "Total consumption amount of users", "用户总余额：": "Total balance of users: ", "用户总数": "Total number of users", "直接注册：": "Direct registration: ", "邀请注册：": "Invitation registration: ", "渠道数量": "Number of channels", "正常：": "Normal: ", "禁用：": "Disabled: ", "测试禁用：": "Test disabled: ", "兑换码发行量": "Redemption code circulation", "已使用: ": "Used: ", "未使用: ": "Unused: ", "密码重置": "Password reset", "处理中...": "Processing...", "操作失败，重定向至登录界面中...": "Operation failed, redirecting to the login page...", "出现错误，第 ${count} 次重试中...": "An error occurred, retrying for the ${count} time...", "飞书 登录": "<PERSON><PERSON><PERSON>", "注册": "Sign up", "已经有帐号了?点击登录": "Already have an account? Login here", "密码重置确认": "Password Reset Confirmation", "使用 Github 登录": "Login with GitHub", "使用 Wechat 登录": "Login with WeChat", "使用飞书登录": "Login with <PERSON><PERSON><PERSON>", "用户名/邮箱是必填项": "Username/email is required", "密码是必填项": "Password is required", "用户名/邮箱": "Username/email", "密码": "Password", "记住我": "Remember me", "忘记密码?": "Forget password?", "请输入邮箱": "Please enter email", "请稍后几秒重试，Turnstile 正在检查用户环境！": "Please try again in a few seconds, Turnstile is checking the user environment!", "用户名是必填项": "Username is required", "确认密码是必填项": "Confirm password is required", "两次输入的密码不一致": "The two passwords entered do not match", "必须是有效的Email地址": "Must be a valid email address", "Email是必填项": "Email is required", "验证码是必填项": "Verification code is required", "用户名": "Username", "确认密码": "Confirm password", "发送验证码": "Send verification code", "验证码": "Verification Code", "重置邮件发送成功，请检查邮箱！": "Reset email sent successfully, please check your email!", "服务器错误": "Server error", "重试 (${countdown})": "Retry (${countdown})", "提交": "Submit", "新密码": "New Password", "无效的链接": "Invalid link", "你的新密码是:": "Your new password is:", "请登录后及时修改密码": "Please change your password in time after logging in", "点击重置密码": "Click to reset password", "验证码不能为空": "Verification code cannot be empty", "未知错误": "Unknown error", "微信验证码登录": "WeChat verification code login", "二维码": "QR code", "请使用微信扫描二维码关注公众号，输入「验证码」获取验证码（三分钟内有效）": "Please scan the QR code of WeChat to follow the official account, enter \"verification code\" to get the verification code (valid within three minutes)", "成功更新": "Successfully updated", "条数据": "data", "请输入api版本号": "Please enter the API version number", "反全选": "Deselect all", "全选": "Select all", "替换值": "Replace value", "遍历data 逗号分隔models， 检测是否只有一个model 如果是则排除": "Traverse data, comma-separated models, check if there is only one model, and exclude it", "成功删除": "Successfully deleted", "如果渠道只有一个模型的，将不会显示，请手动去列表删除渠道": "If the channel has only one model, it will not be displayed, please manually delete the channel from the list", "请输入完整模型名称": "Please enter the full model name", "删除": "Delete", "Azure 版本号": "Azure version number", "批量删除模型": "Batch delete models", "名称 不能为空": "Name cannot be empty", "渠道 不能为空": "Channel cannot be empty", "密钥 不能为空": "Key cannot be empty", "模型 不能为空": "Model cannot be empty", "用户组 不能为空": "User group cannot be empty", "渠道API地址 不能为空": "Channel API address cannot be empty", "必须是有效的JSON字符串": "Must be a valid JSON string", "渠道更新成功！": "Channel updated successfully!", "渠道创建成功！": "Channel created successfully!", "自定义：点击或回车输入": "Custom: Click or press Enter to enter", "编辑渠道": "Edit channel", "新建渠道": "New channel", "填入所有模型": "Fill in all models", "填入渠道支持模型": "Fill in supported model", "，一行一个密钥": ". One key per line", "批量添加": "<PERSON><PERSON> Add", "是否启用": "Enable", "点击测速(仅支持chat模型)": "Click to test speed(Only support chat model)", "仅支持chat模型": "Only support chat model", "上次测速时间：": "Last test time:", "未测试": "Not tested", "手动": "Manual", "自动": "Automatic", "优先级不能小于 0": "Priority cannot be less than 0", "权重不能小于 1": "Weight cannot be less than 1", "请先设置测试模型": "Please set the test model first", "通道 ${item.name}: ${modelName} 测试成功，耗时 ${time.toFixed(2)} 秒。": "Channel ${item.name}: ${modelName} test succeeded, time consumed ${time.toFixed(2)} seconds.", "余额更新成功！": "Balance updated successfully!", "点击更新余额": "Click to update balance", "优先级": "Priority", "权重": "Weight", "测试": "Test", "编辑": "Edit", "复制{' '}": "Copy{' '}", "官网": "Official website", "可用模型:": "Available models:", "测速模型:": "Test model:", "测速模型": "Test model", "代理地址:": "Proxy address:", "其他参数:": "Other parameters:", "其他参数": "Other parameters", "删除通道": "Delete channel", "是否删除通道 {item.name}？": "Delete channel ${item.name}?", "关闭": "Close", "自定义": "Custom", "不支持": "Not supported", "渠道名称": "Channel name", "测试模型": "Test model", "渠道类型": "Channel type", "全部": "All", "状态": "Status", "测速禁用": "Test disabled", "分组": "Group", "渠道API地址": "Channel API address", "密钥": "Key", "代理地址": "Proxy address", "模型": "Model", "模型映射关系": "Model mapping relationship", "用户组": "User group", "请选择渠道类型": "Please select channel type", "请为渠道命名": "Please name the channel", "可空，请输入中转API地址，例如通过cloudflare中转": "Optional, please enter the transfer API address, for example, through cloudflare transfer", "请输入渠道对应的鉴权密钥": "Please enter the authentication key corresponding to the channel", "用于测试使用的模型，为空时无法测速,如：gpt-3.5-turbo，仅支持chat模型": "The model used for testing, cannot be tested when empty, such as: gpt-3.5-turbo，Only support chat model.", "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的": "Please select the models supported by the channel. You can also enter the wildcard * to match the model, for example: gpt-3.5*, which means that all models starting with gpt-3.5 are supported. The * can only be used in the last position, and there must be characters in front of it. For example: gpt-3.5* is correct, *gpt-3.5 is incorrect", "请输入要修改的模型映射关系，格式为：api请求模型ID:实际转发给渠道的模型ID，使用JSON数组表示，例如：": "Please enter the model mapping relationship to be modified, the format is: api request model ID: model ID actually forwarded to the channel, represented by a JSON array, for example:", "请选择该渠道所支持的用户组": "Please select the user group supported by this channel", "默认 API 版本": "Default API Version", "请填写AZURE_OPENAI_ENDPOINT": "Please fill in AZURE_OPENAI_ENDPOINT", "请输入默认API版本，例如：2023-06-01-preview": "Please enter the default API version, for example: 2023-06-01-preview", "按照如下格式输入：APIKey|SecretKey": "Enter in the following format: API Key|Secret", "插件参数": "Plugin parameters", "请输入插件参数，即 X-DashScope-Plugin 请求头的取值": "Please enter the plugin parameters, that is, the value of the X-DashScope-Plugin request header", "版本号": "Version number", "按照如下格式输入：APPID|APISecret|APIKey": "Enter in the following format: APPID|API Secret|API Key", "请输入版本号，例如：v3.1": "Please enter the version number, for example: v3.1", "按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041": "Enter in the following format: API Key-AppId, for example: fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041", "按照如下格式输入：AppId|SecretId|SecretKey": "Enter in the following format: AppId|SecretId|SecretKey", "请输入版本号，例如：v1": "Please enter the version number, for example: v1", "请输入请求地址，例如：https://eastasia.tts.speech.microsoft.com": "Please enter the request address, for example: https://eastasia.tts.speech.microsoft.com", "按照如下格式输入：APISecret|groupID": "Enter in the following format: API Secret|groupID", "按照如下格式输入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空": "Enter in the following format: Region|AccessKeyID|SecretAccessKey|SessionToken, where SessionToken can be left blank", "密钥填写midjourney-proxy的密钥，如果没有设置密钥，可以随便填": "The key is the key of midjourney-proxy. If the key is not set, you can fill it in at will", "地址填写midjourney-proxy部署的地址": "The address is the address where midjourney-proxy is deployed", "按照如下格式输入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN": "Enter in the following format: CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN", "知识库": "Knowledge base", "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。": "Please go to the knowledge base of the open platform to upload documents, and then use the knowledge base function for retrieval.", "知识库ID": "Knowledge base ID", "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)": "When it comes to the knowledge base ID, please go to the knowledge base module of the open platform to create or obtain (it is the knowledge base ID, not the document ID!)", "知识库模板": "Knowledge base template", "请求模型时的知识库模板, 请查看文档填写，否则不用填写": "The knowledge base template when requesting the model, please refer to the document for filling in, otherwise no need to fill in", "网页搜索": "Web search", "使用网页搜索功能，对用户输入的内容进行搜索": "Use the web search function to search the content entered by the user", "是否启用网页搜索": "Enable web search", "声音映射": "Voice mapping", "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy": "Map OpenAI's voice roles to <PERSON><PERSON>'s voice roles, if there is a role, please use | to separate, for example zh-CN-YunxiNeural|boy", "alloy 映射": "<PERSON><PERSON> mapping", "默认 zh-CN-YunxiNeural": "Default zh-CN-YunxiNeural", "echo 映射": "Echo mapping", "默认 zh-CN-YunyangNeural": "Default zh-CN-YunyangNeural", "fable 映射": "Fable mapping", "默认 zh-CN-YunxiNeural|boy": "Default zh-CN-YunxiNeural|boy", "onyx 映射": "Onyx mapping", "默认 zh-CN-YunyeNeural": "Default zh-CN-YunyeNeural", "nova 映射": "Nova mapping", "默认 zh-CN-XiaochenNeural": "Default zh-C<PERSON>-XiaochenNeural", "shimmer 映射": "Shimmer mapping", "默认 zh-CN-XiaohanNeural": "Default zh-C<PERSON>-XiaohanNeural", "操作成功完成！": "Operation completed successfully!", "已成功开始测试所有通道，请刷新页面查看结果。": "All channels have been successfully tested, please refresh the page to view the results.", "已删除所有禁用渠道，共计 ${data} 个": "Removed all disabled channels, total ${data}", "已更新完毕所有已启用通道余额！": "The balance of all enabled channels has been updated!", "渠道": "Channel", "批量处理": "Batch processing", "优先级/权重解释：": "Priority/weight explanation:", "优先级越大，越优先使用；(只有该优先级下的节点都冻结或者禁用了，才会使用低优先级的节点)": "Higher priority nodes are prioritized over lower ones. Low-priority nodes only being utilized when all nodes of higher priority are frozen or disabled.", "相同优先级下：根据权重进行负载均衡(加权随机)": "In the same priority level: Load balancing based on weights (weighted random)", "如果在设置-通用设置中设置了“重试次数”和“重试间隔”，则会在失败后重试。": "If \"Retry Count\" and \"Retry Interval\" are set in the General Settings, retries will occur after a failure", "重试逻辑：1）先在高优先级中的节点重试，如果高优先级中的节点都冻结了，才会在低优先级中的节点重试。2）如果设置了“重试间隔”，则某一渠道失败后，会冻结一段时间，所有人都不会再使用这个渠道，直到冻结时间结束。3）重试次数用完后，直接结束。": "Retry Logic: 1) Retry first in nodes with high priority. If all nodes in high priority are frozen, retry will occur in nodes with low priority. 2) If 'Retry Interval' is set, upon failure of a particular channel, it will be frozen for a period, and no one will use this channel until the freeze period ends. 3) After exhausting the retry count, the process will end directly.", "刷新/清除搜索条件": "Refresh/Clear search criteria", "搜索": "Search", "测试所有渠道": "Test All", "更新启用余额": "Update balance", "删除禁用渠道": "Delete Disabled Channels", "名称": "Name", "类型": "Type", "响应时间": "Response time", "余额": "Balance", "已使用": "Used", "操作": "Action", "当前可用模型": "Current available models", "其他模型": "Other models", "今日请求": "Today's requests", "今日消费": "Today's consumption", "今日Token": "Today's Token", "余 额:": "Balance:", "已使用:": "Used:", "调用次数:": "Requests:", "7日总消费：$": "Total consumption in 7 days: $", "返回": "Back", "加载首页内容失败...": "Failed to load home page content...", "正在跳转中...": "Redirecting...", "未知{' '}": "Unknown{' '}", "无": "None", "令牌名称": "Token Name", "起始时间": "Start Time", "结束时间": "End Time", "渠道ID": "Channel ID", "用户名称": "Username", "'全部'": "'All'", "'充值'": "'Billing'", "'消费'": "'Consumption'", "'管理'": "'Management'", "'系统'": "'System'", "日志": "Log", "时间": "Time", "用户": "User", "令牌": "Token", "耗时": "Duration", "t/s：输出Token的数量除以总生成时间，表示生成速度": "Generation speed (t/s): The number of output tokens divided by the total generation time.", "输入": "Input", "输出": "Output", "额度": "<PERSON><PERSON><PERSON>", "详情": "Details", "显示": "Show", "图片地址": "Image address", "复制地址": "Copy address", "下载图片{' '}": "Download image{' '}", "新窗口打开{' '}": "Open in new window{' '}", "任务ID": "Task ID", "绘图": "Imagine", "放大": "Upscale", "变换": "Variation", "强变换": "High variation", "弱变换": "Low variation", "平移": "Translation", "图生文": "Describe", "图混合": "Mix", "缩词": "Abbreviation", "重绘": "<PERSON><PERSON>", "局部重绘-提交": "Partial redraw-submit", "变焦": "Zoom", "自定义变焦-提交": "Custom zoom-submit", "窗口处理": "Window processing", "换脸": "Face swap", "已提交": "Submitted", "等待中": "Waiting", "重复提交": "Resubmit", "未提交": "Not submitted", "成功": "Success", "未启动": "Not started", "队列中": "In queue", "执行中": "In progress", "失败": "Failed", "窗口等待": "Window waiting", "提交时间": "Submission time", "提交结果": "Submission result", "任务状态": "Task status", "进度": "Progress", "结果图片": "Result image", "失败原因": "Failure reason", "供应商": "Provider", "输入(/1k tokens)": "Input(/1k tokens)", "输出(/1k tokens)": "Output(/1k tokens)", "无可用模型": "No available models", "数据格式不正确": "Incorrect data format", "请先获取数据": "Please get the data first", "没有新增模型": "No new models", "输入倍率由 ${model.input} 变为 ${newModel.input},": "The input rate changes from ${model.input} to ${newModel.input},", "输出倍率由 ${model.output} 变为 ${newModel.output}": "The output rate changes from ${model.output} to ${newModel.output}", "检查更新": "Check for updates", "获取数据": "Get data", "无更新": "No updates", "新增模型：": "New model:", "价格变动模型(仅供参考，如果你自己修改了对应模型的价格请忽略)：": "Price change model (for reference only, if you modify the price of the corresponding model yourself, please ignore):", "注意:": "Note:", "你可以选择覆盖或者仅添加新增，如果你选择覆盖，将会删除你自己添加的模型价格，完全使用远程配置，如果你选择仅添加新增，将会只会添加": "You can choose to overwrite or only add new ones. If you choose to overwrite, the model price you added will be deleted, and the remote configuration will be used completely. If you choose to add only new ones, only new ones will be added", "新增模型的价格": "Price of new models", "覆盖数据": "Overwrite data", "仅添加新增": "Add only new", "类型 错误": "Type error", "类型 不能为空": "Type cannot be empty", "渠道类型 错误": "Channel type error", "渠道类型 不能为空": "Channel type cannot be empty", "输入倍率 不能为空": "Input rate cannot be empty", "输出倍率 不能为空": "Output rate cannot be empty", "保存成功！": "Saved successfully!", "新建": "New", "输入倍率": "Input rate", "输出倍率": "Output rate", "请选择该价格所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的{' '}": "Please select the models supported by this price. You can also enter the wildcard * to match the model, for example: gpt-3.5*, which means that all models starting with gpt-3.5 are supported. The * can only be used in the last position, and there must be characters in front of it. For example: gpt-3.5* is correct, *gpt-3.5 is incorrect{' '}", "删除价格组": "Delete price group", "是否删除价格组？": "Delete price group?", "按Token收费": "Charged by <PERSON><PERSON>", "按次收费": "Charged by times", "<b>美元</b>：1 === $0.002 / 1K tokens <b>人民币</b>： 1 === ￥0.014 / 1k tokens": "<b>USD</b>: 1 === $0.002 / 1K tokens <b>CNY</b>: 1 === ￥0.014 / 1k tokens", "<br /> <b>例如</b>：<br /> gpt-4 输入： $0.03 / 1K tokens 完成：$0.06 / 1K tokens <br />": "<br /> <b>For example (GPT-4)</b>:<br /> input: $0.03 / 1K tokens; output: $0.06 / 1K tokens. <br />", "0.03 / 0.002 = 15, 0.06 / 0.002 = 30，即输入倍率为 15，完成倍率为 30": "So, 0.03 / 0.002 = 15 and 0.06 / 0.002 = 30. The input rate is 15 and the output (completion) rate is 30.", "存在未配置价格的模型，请及时配置价格": "There are models without price configuration, please configure the price in time", "存在供应商类型错误的模型，请及时配置": "There are models with incorrect supplier types, please configure them in time", "更新价格": "Update price", "单条操作": "Single operation", "合并操作": "Merge operation", "刷新": "Refresh", "模型数量": "Model quantity", "模型名称不能为空": "Model name cannot be empty", "类型只能是tokens或times": "Type can only be tokens or times", "所属渠道类型错误": "The channel type is incorrect", "模型名称不能重复": "Model name cannot be repeated", "输入倍率必须大于等于0": "The input rate must be greater than or equal to 0", "输出倍率必须大于等于0": "The output rate must be greater than or equal to 0", "新增": "Add", "保存成功": "Saved successfully", "确定删除?": "Are you sure you want to delete?", "确定删除 ${selectedRow?.model} 吗？": "Are you sure you want to delete ${selectedRow?.model}?", "请输入正确的邮箱地址": "Please enter the correct email address", "邮箱不能为空": "Email cannot be empty", "邮箱账户绑定成功！": "Email address linked successfully!", "绑定邮箱": "Link Email Address", "重新发送(${countdown})": "Resend(${countdown})", "获取验证码": "Get Verification Code", "用户名 不能为空": "Username cannot be empty", "用户名 不能小于 3 个字符": "Username cannot be less than 3 characters", "密码不能小于 8 个字符": "Password cannot be less than 8 characters", "微信账户绑定成功！": "WeChat account linked successfully!", "用户信息更新成功！": "User information updated successfully!", "未绑定": "Not Linked", "个人信息": "Personal information", "请输入用户名": "Please enter username", "请输入密码": "Please enter password", "显示名称": "Display Name", "请输入显示名称": "Please enter a display name", "账号绑定": "Account <PERSON><PERSON>", "绑定微信账号": "<PERSON> Account", "绑定GitHub账号": "<PERSON> Account", "绑定 飞书 账号": "<PERSON> Account", "更换邮箱": "Change email", "Telegram 机器人": "Telegram Bot", "点击下方按钮，将会在 Telegram 中打开 机器人，点击 /start 开始。": "Click the button below to open the bot in Telegram, click /start to start.", "向机器人发送/bind命令后，输入下方的访问令牌即可绑定。(如果没有生成，请点击下方按钮生成)": "After sending the /bind command to the bot, enter the access token below to bind. (If not generated, click the button below to generate)", "其他": "Other", "注意，此处生成的令牌用于系统管理，而非用于请求 OpenAI 相关的服务，请知悉。": "Note: The token generated here is intended for system management purposes only. It is not designed for use in requesting OpenAI related services.", "你的访问令牌是:": "Your access token is:", "请妥善保管。如有泄漏，请立即重置。": "Kindly ensure the safekeeping of your access token. In the event of any security breach, we advise promptly resetting it for your protection.", "重置访问令牌": "Reset access token", "生成访问令牌": "Generate access token", "必须大于等于0": "Must be greater than or equal to 0", "必须大于等于1": "Must be greater than or equal to 1", "兑换码更新成功！": "Redemption code updated successfully!", "兑换码创建成功！": "Redemption code created successfully!", "编辑兑换码": "Edit redemption code", "新建兑换码": "New redemption code", "更新兑换码信息": "Update redemption code information", "创建新的兑换码": "Create a new redemption code", "数量": "Quantity", "尚未兑换": "Not yet redeemed", "兑换码": "Redeem Code", "复制": "Copy", "删除兑换码": "Delete redemption code", "是否删除兑换码 {item.name}？": "Delete redemption code ${item.name}?", "兑换": "Redeem", "搜索兑换码的ID和名称...": "Search for redemption code ID and name...", "创建时间": "Creation Time", "兑换时间": "Redemption Time", "设置成功！": "Set successfully!", "分组倍率不是合法的 JSON 字符串": "Group rate is not a valid JSON string", "单位额度、重试次数、冷却时间不能为负数": "Unit quota, retry times, and cooldown time cannot be negative", "${data} 条日志已清理！": "${data} logs have been cleared!", "日志清理失败：": "Log cleaning failed:", "通用设置": "General Settings", "充值链接": "Redemption Link", "例如发卡网站的购买链接": "For example, the purchase link of the card issuing website", "聊天链接": "Playground link", "例如 ChatGPT Next Web 的部署地址": "For example, the deployment address of ChatGPT Next Web", "单位额度": "Unit quota", "一单位货币能兑换的额度": "Quota that can be exchanged for one unit of currency", "重试次数": "Retry count", "重试间隔(秒)": "Retry interval (seconds)", "以货币形式显示额度": "Display quota in currency format", "Billing 相关 API 显示令牌额度而非用户额度": "Billing APIs show token quotas instead of user quotas", "使用近似的方式估算 token 数以减少计算量": "Approximate token counting to reduce computational cost", "保存通用设置": "Save General Set<PERSON>s", "Midjourney 允许回调（会泄露服务器ip地址）": "Midjourney allows callbacks (will leak the server IP address)", "是否开启聊天缓存(如果没有启用Redis，将会存储在数据库中)": "Whether to enable chat cache (if Redis is not enabled, it will be stored in the database)", "缓存时间(分钟)": "Cache time (minutes)", "开启缓存时，数据缓存的时间": "When cache is enabled, the time the data is cached", "保存其他设置": "Save Other Settings", "日志设置": "Log Settings", "启用日志消费": "Enable log consumption", "日志清理时间": "Log cleaning time", "清理历史日志": "Clear log history", "监控设置": "Monitoring Settings", "最长响应时间": "Longest Response Time", "单位秒，当运行通道全部测试时，超过此时间将自动禁用通道": "Unit seconds, when running all channel tests, the channel will be automatically disabled if it exceeds this time", "额度提醒阈值": "Quota reminder threshold", "低于此额度时将发送邮件提醒用户": "Email will be sent to remind users when the quota is below this", "失败时自动禁用通道": "Automatically disable the channel when it fails", "成功时自动启用通道": "Automatically enable the channel when successful", "保存监控设置": "Save Monitoring Settings", "额度设置": "<PERSON><PERSON><PERSON>", "新用户初始额度": "Initial quota for new users", "例如：100": "For example: 100", "请求预扣费额度": "Request for pre-deducted quota", "请求结束后多退少补": "Refund more or less after the request ends", "邀请新用户奖励额度": "Invite new users to reward quota", "例如：2000": "For example: 2000", "新用户使用邀请码奖励额度": "New user rewards quota using invitation code", "例如：1000": "For example: 1000", "保存额度设置": "Save <PERSON><PERSON><PERSON>", "倍率设置": "Rate Settings", "分组倍率": "Group rate", "为一个 JSON 文本，键为分组名称，值为倍率": "Is a JSON text, the key is the group name, and the value is the rate", "保存倍率设置": "Save Rate Settings", "无法获取当前版本号": "Unable to get the current version number", "已是最新版本：${tag_name}": "The latest version: ${tag_name}", "已是最新版本：${newVersion}": "The latest version: ${newVersion}", "当前版本：{process.env.VITE_APP_VERSION}": "Current version: {process.env.VITE_APP_VERSION}", "公告": "Announcement", "在此输入新的公告内容，支持 Markdown & HTML 代码": "Enter the new announcement content here, supports Markdown & HTML code", "保存公告": "Save Announcement", "个性化设置": "Personalization Settings", "系统名称": "System Name", "在此输入系统名称": "Enter the system name here", "设置系统名称": "Set system name", "Logo 图片地址": "Logo image address", "在此输入Logo 图片地址": "Enter the Logo image address here", "设置 Logo": "Set <PERSON>", "首页内容": "Home Page Content", "在此输入首页内容，支持 Markdown & HTML 代码，设置后首页的状态信息将不再显示。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为首页。": "Enter the homepage content here, supports Markdown & HTML code. Once set, the status information of the homepage will not be displayed. If a link is entered, it will be used as the src attribute of the iframe, allowing you to set any webpage as the homepage.", "保存首页内容": "Save Home Page Content", "在此输入新的关于内容，支持 Markdown & HTML 代码。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为关于页面。": "Enter new about content here, supports Markdown & HTML code. If a link is entered, it will be used as the src attribute of the iframe, allowing you to set any webpage as the about page.", "保存关于": "Save About", "在此输入新的页脚，留空则使用默认页脚，支持 HTML 代码": "Enter the new footer here, leave it blank to use the default footer, supports HTML code", "设置页脚": "<PERSON>", "新版本：{updateData.tag_name}": "New version: {updateData.tag_name}", "去GitHub查看": "View on GitHub", "服务器地址": "Server Address", "例如：https://yourdomain.com": "For example: https://yourdomain.com", "更新服务器地址": "Update Server Address", "配置登录注册": "Configure Login/Registration", "允许通过密码进行登录": "Allow login via password", "允许通过密码进行注册": "Allow registration via password", "通过密码注册时需要进行邮箱验证": "Email verification is required when registering via password", "允许通过 GitHub 账户登录 & 注册": "Allow login & registration via GitHub", "允许通过微信登录 & 注册": "Allow login & registration via WeChat", "允许新用户注册（此项为否时，新用户将无法以任何方式进行注册）": "Allow new user registration (when disabled, new users will not be able to register in any way)", "启用 Turnstile 用户校验": "Enable Turnstile user verification", "配置邮箱域名白名单": "Configure Email Domain Whitelist", "用以防止恶意用户利用临时邮箱批量注册": "This helps to prevent bulk registration using disposable email addresses", "启用邮箱域名白名单": "Enable Email Domain Whitelist", "允许的邮箱域名": "Allowed Email Domain", "保存邮箱域名白名单设置": "Save Email Domain Whitelist Settings", "配置 SMTP": "Configure SMTP", "用以支持系统的邮件发送": "Used to support the system's email sending function", "SMTP 服务器地址": "SMTP Server Address", "例如：smtp.qq.com": "For example: smtp.mailgun.com", "SMTP 端口": "SMTP Port", "默认: 587": "Default: 587", "SMTP 账户": "SMTP Account", "通常是邮箱地址": "Usually an email address", "SMTP 发送者邮箱": "SMTP Sender Email", "通常和邮箱地址保持一致": "Usually consistent with the email address", "SMTP 访问凭证": "SMTP Access Credential", "敏感信息不会发送到前端显示": "Sensitive information will not be displayed in the frontend", "保存 SMTP 设置": "Save SMTP Settings", "配置 GitHub OAuth App": "Configure GitHub OAuth App", "用以支持通过 GitHub 进行登录注册，": "To support login & registration via GitHub, {' '}", "点击此处": "click here to", "管理你的 GitHub OAuth App": "{' '} manage your GitHub OAuth App", "Homepage URL 填": "Fill in the Homepage URL", "Authorization callback URL 填": "Fill in the Authorization callback URL", "输入你注册的 GitHub OAuth APP 的 ID": "Enter your registered GitHub OAuth APP ID", "保存 GitHub OAuth 设置": "Save GitHub OAuth Settings", "配置 WeChat Server": "Configure WeChat Server", "用以支持通过微信进行登录注册，": "To support login & registration via WeChat, {' '}", "了解 WeChat Server": "{' '} learn about WeChat Server", "WeChat Server 服务器地址": "WeChat Server server address", "WeChat Server 访问凭证": "WeChat Server Access Credential", "微信公众号二维码图片链接": "WeChat Public Account QR Code Image Link", "输入一个图片链接": "Enter an image link", "保存 WeChat Server 设置": "Save WeChat Server Settings", "配置飞书授权登录": "Configure <PERSON><PERSON><PERSON> authorization login", "用以支持通过飞书进行登录注册，": "To support login & registration via Feishu, {' '}", "管理你的飞书应用": "{' '} manage your Feishu application", "主页链接填": "Fill in the Homepage URL", "重定向 URL 填": "Fill in the Redirect URL", "输入 App ID": "Enter App ID", "保存飞书 OAuth 设置": "Save <PERSON><PERSON><PERSON>", "配置 Turnstile": "Configure Turn<PERSON><PERSON>", "用以支持用户校验，": "To support user verification, {' '}", "管理你的 Turnstile Sites，推荐选择 Invisible Widget Type": "{' '} manage your Turnstile Site. Recommended: Invisible Widget Type", "输入你注册的 Turnstile Site Key": "Enter your registered Turnstile Site Key", "保存 Turnstile 设置": "Save Turn<PERSON><PERSON> Settings", "警告": "Warning", "取消密码登录将导致所有未绑定其他登录方式的用户（包括管理员）无法通过密码登录，确认取消？": "Canceling password login will cause all users (including administrators) who have not linked other login methods to be unable to log in via password, confirm cancel?", "确定": "Confirm", "运营设置": "Operation Settings", "系统设置": "System Settings", "其他设置": "Other Settings", "命令 不能为空": "Command cannot be empty", "说明 不能为空": "Description cannot be empty", "消息类型 不能为空": "Message type cannot be empty", "消息内容 不能为空": "Message content cannot be empty", "菜单更新成功！": "Menu updated successfully!", "菜单创建成功！": "<PERSON><PERSON> created successfully!", "编辑菜单": "Edit menu", "新建菜单": "New menu", "命令": "Command", "说明": "Description", "消息类型": "Message type", "消息内容": "Message content", "删除菜单": "Delete menu", "是否删除菜单 {item.name}？": "Delete menu ${item.name}?", "重载成功！": "Reloaded successfully!", "Telegram Bot菜单": "Telegram Bot menu", "添加修改菜单命令/说明后（如果没有修改命令和说明可以不用重载），需要重新载入菜单才能生效。": "After adding or modifying menu commands/descriptions (if there are no modified commands and descriptions, reloading is not required), you need to reload the menu to take effect.", "如果未查看到新菜单，请尝试杀后台后重新启动程序。": "If the new menu is not displayed, try killing the background and restarting the program.", "在线": "Online", "离线": "Offline", "重新载入菜单": "Reload menu", "搜索ID和命令...": "Search ID and command...", "回复类型": "Reply type", "回复内容": "Reply content", "令牌更新成功！": "Token updated successfully!", "令牌创建成功，请在列表页面点击复制获取令牌！": "<PERSON><PERSON> created successfully, please click copy on the list page to get the token!", "编辑令牌": "Edit token", "新建令牌": "New token", "注意，令牌的额度仅用于限制令牌本身的最大额度使用量，实际的使用受到账户的剩余额度限制。": "Note that the quota of the token is only used to limit the maximum quota usage of the token itself, and the actual usage is limited by the remaining quota of the account.", "过期时间": "Expiration time", "无效的日期": "Invalid date", "永不过期": "Never expires", "无限额度": "Unlimited quota", "是否开启缓存(开启后，将会缓存聊天记录，以减少消费)": "Whether to enable cache (after enabling, chat records will be cached to reduce consumption)", "已启用": "Enabled", "已禁用": "Disabled", "已过期": "Expired", "已耗尽": "Exhausted", "无限制": "Unlimited", "已复制到剪贴板！": "Copied to clipboard!", "删除Token": "Delete Token", "是否删除Token {item.name}？": "Delete Token ${item.name}?", "将OpenAI API基础地址https://api.openai.com替换为<b>{siteInfo.server_address}</b>，复制下面的密钥即可使用。": "Replace the OpenAI API base address https://api.openai.com with <b>{siteInfo.server_address}</b>, and copy the key below to use.", "搜索令牌的名称...": "Search for token name...", "已用额度": "Balance used", "剩余额度": "Remaining balance", "邀请链接": "Invitation link", "邀请奖励": "Invitation reward", "分享您的邀请链接，邀请好友注册，即可获得奖励!": "Share your unique invitation link with friends. By inviting them to register, both you and your friends will earn rewards!", "点击生成邀请链接": "Click to generate an invitation link", "生成": "Generate", "请输入充值码！": "Please enter the redemption code!", "充值成功！": "Recharge successful!", "请求失败": "Request failed", "超级管理员未设置充值链接！": "Super administrator has not set the redemption link!", "当前额度:": "Credit remaining:", "请输入兑换码": "Please enter the redemption code", "兑换中...": "Redeeming...", "还没有兑换码？ 点击获取兑换码：": "No redemption code yet? Click to get the redemption code:", "获取兑换码": "Get Redeem Code", "充值记录以及邀请记录请在日志中查询。充值记录请在日志中选择类型【充值】查询；邀请记录请在日志中选择【系统】查询{' '}": "Recharge records and invitation records can be found in the logs. Recharge records can be found by selecting the type [Recharge] in the logs; invitation records can be found by selecting [System] in the logs{' '}", "密码 不能为空": "Password cannot be empty", "额度 不能小于 0": "Quota cannot be less than 0", "用户更新成功！": "User updated successfully!", "用户创建成功！": "User created successfully!", "编辑用户": "Edit user", "新建用户": "New user", "普通用户": "Regular User", "管理员": "Admin", "超级管理员": "Super Admin", "未知身份": "Unknown Identity", "请求次数": "Number of Requests", "设为管理员": "Set as administrator", "取消管理员": "Cancel administrator", "删除用户": "Delete User", "是否删除用户 {item.name}？": "Are you sure to delete user ${item.name}?", "搜索用户的ID，用户名，显示名称，以及邮箱地址...": "Search user ID, username, display name, and email address...", "统计信息": "Statistics", "用户角色": "Role", "个人设置": "Account <PERSON><PERSON>", "模型价格": "Pricing", "可用模型": "Available models", "仪表盘": "Dashboard", "分析": "Analytics", "开始时间": "Start Time", "模型名称为coze-{bot_id}，你也可以直接使用 coze-* 通配符来匹配所有coze开头的模型": "The model name is coze-{bot_id}, you can also directly use the coze-* wildcard to match all models starting with coze", "模型名称映射， 你可以取一个容易记忆的名字来代替coze-{bot_id}，例如：": "Model name mapping, you can take an easy-to-remember name to replace coze-{bot_id}, for example: ", ",注意：如果使用了模型映射，那么上面的模型名称必须使用映射前的名称，上述例子中，你应该在模型中填入coze-translate(如果已经使用了coze-*，可以忽略)。": ", Note: If a model mapping is used, then the model name above must use the name before the mapping. In the example above, you should fill in coze-translate in the model (if coze-* has been used, it can be ignored).", "位置/区域": "Location/Region", "请输入你 Speech Studio 的位置/区域，例如：eastasia": "Please enter the location/region of your Speech Studio, for example: eastasia", "必须指定渠道": "Channel must be specified", "中继": "<PERSON><PERSON>", "请输入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": "Please enter the Ollama address you deployed, for example: http://127.0.0.1:11434. If you are using Cloudflare Zero Trust, you can fill in the authorization information in the plugin below.", "请随意填写": "Please fill in at will", "Header 配置": "Header Configuration", "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header": "This configuration is mainly used for the header that needs to be configured when using Cloudflare Zero Trust to expose the port to the public network"}